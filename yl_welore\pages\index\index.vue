<template>
  <view>
    <cu-custom bgColor="bg-white" :isSearch="true">
      <view slot="content" style="color: #2c2b2b;font-weight: 600; font-size: 36rpx;">{{ $state.diy.home_title }}</view>
    </cu-custom>
    <add-tips duration="10"></add-tips>
    <view :class="'cu-modal ' + (indexImage_c ? 'show' : '')">
      <view class="cu-dialog" style="width: 280px;background: transparent;">
        <view class="bg-img">
          <swiper :circular="true" :skip-hidden-item-layout="true" :autoplay="true" interval="5000" duration="1000"
            v-if="indexImage_c" style="overflow: hidden;height: 405px;">
            <template v-for="(item, index) in info_top">
              <swiper-item v-if="item.practice_type == 0">
                <image @tap.stop.prevent="open_navigator" :data-src="item.playbill_url" :src="item.playbill_url"
                  :data-type="item.practice_type" :data-url="item.url" mode="widthFix"
                  style="width:270px;border-radius: 5px;"></image>
              </swiper-item>
              <swiper-item v-if="item.practice_type == 1">
                <image @tap.stop.prevent="open_navigator" :data-src="item.playbill_url" :src="item.playbill_url"
                  :data-type="item.practice_type" :data-url="item.url" mode="widthFix"
                  style="width:270px;border-radius: 5px;"></image>
              </swiper-item>
              <swiper-item v-if="item.practice_type == 2">
                <image @tap.stop.prevent="open_navigator" :src="item.playbill_url" :data-src="item.playbill_url"
                  :data-type="item.practice_type" :data-url="item.url" :data-path="item.wx_app_url" mode="widthFix"
                  style="width:270px;border-radius: 5px;"></image>
              </swiper-item>
            </template>
          </swiper>
        </view>
        <view class="cu-bar center_text" style="z-index:500;" @tap="off_index">
          <text class="_icon-close-round-o" style="color:#ffffff;font-size: 30px;"></text>
          <!-- <view class="action margin-0 flex-sub" bindtap="off_index">关闭</view> -->
        </view>
      </view>
    </view>
    <Tab1 v-if="$state.diy.mod.home == '0'" @get_nearby_realms="get_nearby_realms" :parentData="currentInstance"
      @set_one="set_one" @open_navigator="open_navigator" @handleChange="handleChange" @gambit_list="gambit_list"
      @bindchange_top="bindchange_top" @cardSwiper="cardSwiper" @top_url="top_url" @get_all_qq="get_all_qq"
      @nex_my_qq="nex_my_qq" @this_url="this_url"></Tab1>
    <Tab2 v-if="$state.diy.mod.home == 'aa3b1c88-2d41-9cde-cff7-55372169e4eb'" :parentData="currentInstance"
      @get_nearby_realms="get_nearby_realms" @set_one="set_one" @open_navigator="open_navigator"
      @handleChange="handleChange" @gambit_list="gambit_list" @bindchange_top="bindchange_top" @cardSwiper="cardSwiper"
      @top_url="top_url" @get_all_qq="get_all_qq" @nex_my_qq="nex_my_qq" @this_url="this_url"></Tab2>
    <Tab3 v-if="$state.diy.mod.home == 'be454a15-e373-f773-376b-127f3a35d3c6'" :parentData="currentInstance"
      @get_nearby_realms="get_nearby_realms" @set_one="set_one" @open_navigator="open_navigator"
      @handleChange="handleChange" @gambit_list="gambit_list" @bindchange_top="bindchange_top" @cardSwiper="cardSwiper"
      @top_url="top_url" @get_all_qq="get_all_qq" @nex_my_qq="nex_my_qq" @this_url="this_url"></Tab3>
    <Tab2 v-if="$state.diy.mod.home == '453776a4-6724-fd4f-4ff1-48363b245915'" :parentData="currentInstance"
      @get_nearby_realms="get_nearby_realms" @set_one="set_one" @open_navigator="open_navigator"
      @handleChange="handleChange" @gambit_list="gambit_list" @bindchange_top="bindchange_top" @cardSwiper="cardSwiper"
      @top_url="top_url" @get_all_qq="get_all_qq" @nex_my_qq="nex_my_qq" @this_url="this_url"></Tab2>
    <Tab5 v-if="$state.diy.mod.home == 'd5b2d78e-3152-ee54-aca8-a4402adc601b'" :parentData="currentInstance"
      @get_nearby_realms="get_nearby_realms" @set_one="set_one" @open_navigator="open_navigator"
      @handleChange="handleChange" @gambit_list="gambit_list" @bindchange_top="bindchange_top" @cardSwiper="cardSwiper"
      @top_url="top_url" @get_all_qq="get_all_qq" @nex_my_qq="nex_my_qq" @this_url="this_url"></Tab5>
    <Tab6 v-if="$state.diy.mod.home == '58567383-612e-ca8f-116d-89e1057eb02a'" :parentData="currentInstance"
      @get_nearby_realms="get_nearby_realms" @set_one="set_one" @open_navigator="open_navigator"
      @handleChange="handleChange" @gambit_list="gambit_list" @bindchange_top="bindchange_top" @cardSwiper="cardSwiper"
      @top_url="top_url" @get_all_qq="get_all_qq" @nex_my_qq="nex_my_qq" @this_url="this_url"></Tab6>
    <Tab7 v-if="$state.diy.mod.home == '9701db92-a7e1-bdd7-842e-9e9bea168127'" :parentData="currentInstance"
      @get_nearby_realms="get_nearby_realms" @set_one="set_one" @open_navigator="open_navigator"
      @handleChange="handleChange" @gambit_list="gambit_list" @bindchange_top="bindchange_top" @cardSwiper="cardSwiper"
      @top_url="top_url" @get_all_qq="get_all_qq" @nex_my_qq="nex_my_qq" @this_url="this_url"></Tab7>
    <Tab8 v-if="$state.diy.mod.home == '47a436a1-6541-a7a0-5723-61d7fe40b7c3'" :parentData="currentInstance"
      @get_nearby_realms="get_nearby_realms" @set_one="set_one" @open_navigator="open_navigator"
      @handleChange="handleChange" @gambit_list="gambit_list" @bindchange_top="bindchange_top" @cardSwiper="cardSwiper"
      @top_url="top_url" @get_all_qq="get_all_qq" @nex_my_qq="nex_my_qq" @this_url="this_url"></Tab8>
    <Tab7 v-if="$state.diy.mod.home == '57228047-ad66-b5c0-2970-0be62de79377'" :parentData="currentInstance"
      @get_nearby_realms="get_nearby_realms" @set_one="set_one" @open_navigator="open_navigator"
      @handleChange="handleChange" @gambit_list="gambit_list" @bindchange_top="bindchange_top" @cardSwiper="cardSwiper"
      @top_url="top_url" @get_all_qq="get_all_qq" @nex_my_qq="nex_my_qq" @this_url="this_url"></Tab7>
    <Top :parentData="this" @handleClickItem1="handleClickItem1"></Top>
    <Index1 ref="indexComp" v-if="$state.diy.mod.home == '0'" :parentData="currentInstance" @home-url="home_url"
      @gambit-list="gambit_list" @dian-option="dian_option" @vote-do="vote_do" @play="play" @stop="stop"
      @slider-change="sliderChange" @home-pl="home_pl" @dynamic-code="handleDynamicCode"></Index1>
    <Index2 ref="indexComp" v-if="$state.diy.mod.home == 'aa3b1c88-2d41-9cde-cff7-55372169e4eb'"
      :parentData="currentInstance" @home-url="home_url" @gambit-list="gambit_list" @dian-option="dian_option"
      @vote-do="vote_do" @play="play" @stop="stop" @slider-change="sliderChange" @home-pl="home_pl"
      @dynamic-code="handleDynamicCode"></Index2>
    <Index3 ref="indexComp" v-if="$state.diy.mod.home == 'be454a15-e373-f773-376b-127f3a35d3c6'"
      :parentData="currentInstance" @home-url="home_url" @gambit-list="gambit_list" @dian-option="dian_option"
      @vote-do="vote_do" @play="play" @stop="stop" @slider-change="sliderChange" @home-pl="home_pl"
      @dynamic-code="handleDynamicCode"></Index3>
    <Index4 ref="indexComp" v-if="$state.diy.mod.home == '453776a4-6724-fd4f-4ff1-48363b245915'"
      :parentData="currentInstance" @home-url="home_url" @gambit-list="gambit_list" @dian-option="dian_option"
      @vote-do="vote_do" @play="play" @stop="stop" @slider-change="sliderChange" @home-pl="home_pl"
      @dynamic-code="handleDynamicCode"></Index4>
    <Index5 ref="indexComp" v-if="$state.diy.mod.home == 'd5b2d78e-3152-ee54-aca8-a4402adc601b'"
      :parentData="currentInstance" @home-url="home_url" @gambit-list="gambit_list" @dian-option="dian_option"
      @vote-do="vote_do" @play="play" @stop="stop" @slider-change="sliderChange" @home-pl="home_pl"
      @dynamic-code="handleDynamicCode"></Index5>
    <Index6 ref="indexComp" v-if="$state.diy.mod.home == '58567383-612e-ca8f-116d-89e1057eb02a'"
      :parentData="currentInstance" @home-url="home_url" @gambit-list="gambit_list" @dian-option="dian_option"
      @vote-do="vote_do" @play="play" @stop="stop" @slider-change="sliderChange" @home-pl="home_pl"
      @dynamic-code="handleDynamicCode"></Index6>
    <Index7 ref="indexComp" v-if="$state.diy.mod.home == '9701db92-a7e1-bdd7-842e-9e9bea168127'"
      :parentData="currentInstance" @home-url="home_url" @gambit-list="gambit_list" @dian-option="dian_option"
      @vote-do="vote_do" @play="play" @stop="stop" @slider-change="sliderChange" @home-pl="home_pl"
      @dynamic-code="handleDynamicCode"></Index7>
    <Index8 ref="indexComp" v-if="$state.diy.mod.home == '47a436a1-6541-a7a0-5723-61d7fe40b7c3'"
      :parentData="currentInstance" @home-url="home_url" @gambit-list="gambit_list" @dian-option="dian_option"
      @vote-do="vote_do" @play="play" @stop="stop" @slider-change="sliderChange" @home-pl="home_pl"
      @dynamic-code="handleDynamicCode"></Index8>
    <Index9 ref="indexComp" v-if="$state.diy.mod.home == '57228047-ad66-b5c0-2970-0be62de79377'"
      :parentData="currentInstance" @home-url="home_url" @gambit-list="gambit_list" @dian-option="dian_option"
      @vote-do="vote_do" @play="play" @stop="stop" @slider-change="sliderChange" @home-pl="home_pl"
      @dynamic-code="handleDynamicCode"></Index9>
    <!-- <Index10 ref="indexComp" :parentData="currentInstance" @home-url="home_url"
      @gambit-list="gambit_list" @dian-option="dian_option" @vote-do="vote_do" @play="play" @stop="stop"
      @slider-change="sliderChange" @home-pl="home_pl" @dynamic-code="handleDynamicCode"></Index10> -->


    <!-- 帖子操作 -->
    <view :class="'cu-modal ' + (home_pl_check ? 'show' : '')">
      <view class="cu-dialog">
        <view class="cu-bar bg-white justify-end">
          <view class="content">回复</view>
          <view class="action" @tap="hideModal">
            <text class="cuIcon-close text-red"></text>
          </view>
        </view>
        <view class="padding">
          <textarea v-if="home_pl_check" style="min-height:4em;text-align:left;" @input="get_jubao_text"
            :auto-height="true" value="" class="weui-textarea" maxlength="300" placeholder="欢迎评论..." />
        </view>
        <view class="cu-bar bg-white justify-end">
          <view class="action">
            <button class="cu-btn line-green text-green" @tap="hideModal">取消</button>
            <button class="cu-btn bg-green margin-left" @tap="do_user_pl">确定</button>
          </view>
        </view>
      </view>
    </view>
    <!-- 帖子操作 -->
    <view v-if="copyright.conceal == 0">
      <image v-if="copyright.feeling_stipulate == 1 && copyright.feeling_arbor == 1 && version == 0" @tap="open_tape"
        class="now_level" mode="widthFix"
        :style="'width: 70px;position:fixed;bottom: ' + (copyright.direction_bottom) + '%;' + (copyright.bare_direction == 1 ? 'right' : 'left') + ': 2%;z-index: 120;'"
        :src="copyright.bare_img_url"></image>
    </view>
    <!-- 客服 -->
    <!-- <view class="customer-service-container">
      <button open-type="contact" class="customer-service-btn" hover-class="customer-service-btn-hover">
        <text class="customer-service-icon">💬</text>
        <text class="customer-service-text">客服</text>
      </button>
    </view> -->
    <tabbar id="tabbar" :tabbar="tabbar" :activeIndex="0"></tabbar>
    <login id="login" @checkPhoen="onMyEvent($event, { tagId: 'login' })" :check_user_login="check_user_login"></login>
    <phone id="phone" v-if="check_phone_show" @close_phone_modal="closePhoneModal" :check_phone="check_phone_show">
    </phone>
  </view>
</template>

<script>
import addTips from "@/yl_welore/util/add-tips/index";
import tabbar from "@/yl_welore/util/tabbarComponent/modern-tabbar";
import login from "@/yl_welore/util/user_login/login";
import phone from "@/yl_welore/util/user_phone/phone";
import uiTab from "@/yl_welore/colorui/ui-tab/ui-tab";
import Tab1 from "@/yl_welore/pages/index/tab1.vue";
import Tab2 from "@/yl_welore/pages/index/tab2.vue";
import Tab3 from "@/yl_welore/pages/index/tab3.vue";
import Tab5 from "@/yl_welore/pages/index/tab5.vue";
import Tab6 from "@/yl_welore/pages/index/tab6.vue";
import Tab7 from "@/yl_welore/pages/index/tab7.vue";
import Tab8 from "@/yl_welore/pages/index/tab8.vue";
import Top from "@/yl_welore/pages/index/top.vue";
import Index1 from "@/yl_welore/pages/index/index1.vue";
import Index2 from "@/yl_welore/pages/index/index2.vue";
import Index3 from "@/yl_welore/pages/index/index3.vue";
import Index4 from "@/yl_welore/pages/index/index4.vue";
import Index5 from "@/yl_welore/pages/index/index5.vue";
import Index6 from "@/yl_welore/pages/index/index6.vue";
import Index7 from "@/yl_welore/pages/index/index7.vue";
import Index8 from "@/yl_welore/pages/index/index8.vue";
import Index9 from "@/yl_welore/pages/index/index9.vue";
import Index10 from "@/yl_welore/pages/index/index10.vue";
const app = getApp();
var http = require("@/yl_welore/util/http.js");
const innerAudioContext = uni.getBackgroundAudioManager();
let leftHeight = 0,
  rightHeight = 0; //分别定义左右两边的高度
let query;
export default {
  components: {
    addTips,
    tabbar,
    login,
    phone,
    uiTab,
    Tab1,
    Tab2,
    Tab3,
    Tab5,
    Tab6,
    Tab7,
    Tab8,
    Top,
    Index1,
    Index2,
    Index3,
    Index4,
    Index5,
    Index6,
    Index7,
    Index8,
    Index9,
    Index10
  },
  /** 
   * 页面的初始数据
   */
  data() {
    return {
      currentInstance: this, // 通过data属性中转 
      http_root: app.globalData.http_root,
      cardCur: 0,
      index_page: 1,
      index_my_page: 1,
      index_tj_page: 1,
      tabbar: {},
      activeTabIndex: 0, // 当前活跃的tabbar索引
      copyright: {},
      home_current: 'home',
      visible: false,
      actions: [{
        name: '新发',
        type: 'fatie'
      }, {
        name: '新回',
        type: 'huifu'
      }, {
        name: '热门',
        type: 'dianzan'
      }],
      new_list: [],
      leftList: [],
      rightList: [],
      show: true,
      //inputShowed: false,
      //inputVal: "",
      current: 'tab1',
      title: '',
      //导航栏 中间的标题
      di_msg: false,
      ad_info: {},
      diy: {},
      sw_info: [],
      version: 1,
      order_time: 'fatie',
      //发帖时间排序
      images: [],
      home_list: [],
      mod: '',
      design: {},
      user_male: 0,
      indexImage_c: false,
      info_top: [],
      info_home: [],
      level_images: [],
      check_user_login: false,
      swiperCurrent: 0,
      check_phone_show: false,
      home_pl_check: false,
      home_pl_id: 0,
      home_pl_key: 0,
      home_pl_text: '',
      scene: 0,
      home: 1,
      //投票专属
      dian_index: -1,
      //点击投票
      imgheights: [],
      top_index: 0,
      PrevideoID: '',
      //播放ID
      my_qq_list: [],
      my_qq_page: 1,
      floorstatus: false,
      put_top_info: {},
      put_top_list: [],
      near: false,
      near_list: [],
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    uni.showShareMenu({
      menus: ['shareAppMessage', 'shareTimeline']
    });
    var op = uni.getLaunchOptionsSync();
    console.log(op);
    this.scene = op.scene;
    if (op.scene == 1065) {
      app.globalData.removeCache("userinfo");
    }
    if (op.scene == 1154) {
      this.get_index_list_one_async();
      this.get_ad_async();
    } else {
      this.doIt();
    }
    //this.doIt();
    var over_time = app.globalData.getCache("over_time");
    if (!over_time) {
      var n = parseInt(+new Date() / 1000) + 129600;
      app.globalData.setCache("over_time", n);
    }
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

    uni.hideTabBar();
    var copyright = getApp().globalData.store.getState().copyright;
    console.log(copyright);
    if (Object.keys(copyright).length === 0) {
      this.authority();
    } else {
      this.copyright = copyright;
      app.globalData.store.setState({
        copyright: copyright
      });
      this.getHomeFj();
    }
    if (this.show == false) {
      return;
    }



    var over_time = app.globalData.getCache("over_time");
    var dd = uni.getStorageSync('is_diy');
    console.log(dd);
    var this_time = parseInt(+new Date() / 1000);
    if (dd && over_time > this_time) {
      app.globalData.store.setState({
        diy: dd
      });
      app.globalData.editTabbar();
    } else {
      this.get_diy();
    }
    //wx.hideLoading();
  },
  onPageScroll(e) {
    if (e.scrollTop > 1000) {
      this.floorstatus = true;
    } else {
      this.floorstatus = false;
    }
  },

  onPullDownRefresh() {
    var e = app.globalData.getCache("userinfo");
    if (!e) {
      uni.showToast({
        title: '系统错误',
        icon: 'none'
      });
      return;
    }
    //模拟加载
    setTimeout(() => {
      uni.hideNavigationBarLoading(); //完成停止加载
      uni.stopPullDownRefresh(); //停止下拉刷新
    }, 1500);
    this.new_list = [];
    this.index_page = 1;
    this.index_my_page = 1;
    this.index_tj_page = 1;
    this.my_qq_list = [];
    this.my_qq_page = 1;
    if (this.current == 'tab1') {
      this.get_index_list_one();
    }
    if (this.current == 'tab2') {
      this.get_my_index_list();
    }
    if (this.current == 'tab3') {
      this.get_tj_list();
    }
    this.get_ad();
    this.get_my_q();
  },
  onReachBottom() {
    var e = app.globalData.getCache("userinfo");
    if (!e) {
      uni.showToast({
        title: '系统错误',
        icon: 'none'
      });
      return;
    }
    if (this.current == 'tab1') {
      this.index_page = this.index_page + 1;
      this.get_index_list();
    }
    if (this.current == 'tab2') {
      this.index_my_page = this.index_my_page + 1;
      this.get_my_index_list();
    }
    if (this.current == 'tab3') {
      this.index_tj_page = this.index_tj_page + 1;
      this.get_tj_list();
    }
  },
  onShareAppMessage(res) {
    if (res.from === 'button') {
      // 来自页面内转发按钮
      console.log(res.target);
      var D = res.target.dataset;
      var info = this.new_list[D.key];
      console.log(info, "---------");
      return {
        title: info.content,
        path: '/yl_welore/pages/index/index?user_id=' + app.globalData.getCache("userinfo").uid + "&id=" + info.id,
        imageUrl: info.image_part[0]
      };
    } else {
      return {
        title: this.design.share_title,
        path: '/yl_welore/pages/index/index?user_id=' + app.globalData.getCache("userinfo").uid,
        imageUrl: this.design.share_img
      };
    }
  },
  methods: {
    getHomeFj() {
      this.near = app.globalData.__PlugUnitScreen('26d74b3b11d25b14885ca591338a6c06');
      console.log(this.near);
      if (this.near && this.copyright.nearby_is_home_show == 1) {
        app.globalData.getMyLocation((res) => {
          this.get_nearby_realms_list(res);
        });
      }
    },
    get_nearby_realms_list(it) {
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.latitude = it.latitude;
      params.longitude = it.longitude;
      params.count = 12;
      var b = app.globalData.api_root + 'Nameplate/get_nearby_list';
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == "success") {
            this.near_list = res.data.data;
          } else {
            this.near_list = [];
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    // 关闭手机号验证弹窗
    closePhoneModal() {
      this.check_phone_show = false;
    },
    top_url() {
      uni.navigateTo({
        url: '/yl_welore/pages/packageD/top_ten/index'
      });
    },
    get_nearby_realms() {
      console.log(123123);
      uni.navigateTo({
        url: '/yl_welore/pages/packageF/territory_map/index'
      });
    },
    set_one(d) {
      var info = d.currentTarget.dataset;
      if (info.type == 0) {
        uni.navigateTo({
          url: info.url
        });
      }
      if (info.type == 1) {
        uni.navigateTo({
          url: "/yl_welore/pages/web/index?url=" + info.url
        });
      }
      if (info.type == 2) {
        uni.navigateToMiniProgram({
          appId: info.url,
          path: info.path,
          envVersion: 'release',
          success(res) {
            // 打开成功
          }
        });
      }
    },
    open_navigator(d) {
      var info = d.target.dataset;
      console.log(info);
      if (info.url == '') {
        var current = info.src;
        uni.previewImage({
          current: current,
          // 当前显示图片的http链接  
          urls: [current] // 需要预览的图片http链接列表  
        });
      } else {
        if (info.type == 0) {
          uni.navigateTo({
            url: info.url
          });
        }
        if (info.type == 1) {
          uni.navigateTo({
            url: "/yl_welore/pages/web/index?url=" + info.url
          });
        }
        if (info.type == 2) {
          uni.navigateToMiniProgram({
            appId: info.url,
            path: info.path,
            envVersion: 'release',
            success(res) {
              // 打开成功
            }
          });
        }
      }
    },
    open_tape() {
      uni.navigateTo({
        url: '/yl_welore/pages/packageE/notes/index'
      });
    },
    /**
     * 打开客服
     */
    openCustomerService() {
      // 这里可以根据您的需求实现客服功能
      // 例如：跳转到客服页面、打开客服聊天、拨打客服电话等

      // 方式1: 跳转到客服页面
      // uni.navigateTo({
      //   url: '/pages/customer-service/index'
      // });

      // 方式2: 拨打客服电话
      uni.showActionSheet({
        itemList: ['在线客服', '客服电话'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 打开在线客服
            uni.showToast({
              title: '正在为您转接客服...',
              icon: 'loading',
              duration: 2000
            });
            // 这里可以添加具体的在线客服逻辑
          } else if (res.tapIndex === 1) {
            // 拨打客服电话
            uni.makePhoneCall({
              phoneNumber: '************', // 请替换为实际的客服电话
              success: () => {
                console.log('拨打电话成功');
              },
              fail: () => {
                uni.showToast({
                  title: '拨打失败',
                  icon: 'none'
                });
              }
            });
          }
        }
      });
    },
    previewHuiAndImage(e) {
      console.log(e);
      var current = e.target.dataset.src;
      uni.previewImage({
        current: current,
        // 当前显示图片的http链接  
        urls: [current] // 需要预览的图片http链接列表  
      });
    },
    check_img_vido(item) {
      var index = item.currentTarget.dataset.index;
      var key = item.currentTarget.dataset.key;
      console.log(this.new_list[index]);
      var info = this.new_list[index]['image_part'];
      var params = [];
      for (var i = 0; i < info.length; i++) {
        params.push({
          'url': info[i]
        });
      }
      console.log(params);
      uni.previewMedia({
        current: key,
        // 当前媒体的http链接
        sources: params // 需要预览的媒体链接列表
      });
    },
    by_url(item) {
      var info = this.new_list[item.target.dataset.k];
      var id = 'myVideo' + info['id'];
      var videoContext = uni.createVideoContext(id);
      console.log(id);
      if (info['check_look'] == 0 && info['is_buy'] == 1) {
        videoContext.stop();
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/article/index?id=' + info['id'] + '&type=' + info['study_type']
        });
      }
      //判断是否正在有视频播放
      if (this.PrevideoID) {
        //有正在播放的视频
        //判断和上一个视频是否是同一个视频
        if (this.PrevideoID != id) {
          //不是同一个视频就暂停上一个视频播放
          uni.createVideoContext(this.PrevideoID).stop();
          //播放当前视频
          uni.createVideoContext(id).play();
          //更新prevideoID
          this.PrevideoID = id;
        }
      }
      //没有正在播放的视频就直接保存videoID
      else {
        this.PrevideoID = id;
      }
    },
    //投票
    vote_do(item) {
      var index = this.dian_index;
      var key = item.currentTarget.dataset.key;
      if (this.new_list[key].vo_id.length == 0) {
        uni.showToast({
          title: '请选择选项',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.paper_id = this.new_list[key].id;
      params.vo_id = this.agree(this.new_list[key].vo_id);
      var b = app.globalData.api_root + 'Polls/vote_do';
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == "success") {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
            var click = 'new_list[' + key + '].is_vo_check';
            var click1 = 'new_list[' + key + '].vo_count';
            var click2 = 'new_list[' + key + '].vo[' + index + '].voters';
            that.new_list[key].is_vo_check = 1;
            that.new_list[key].vo_count = that.new_list[key].vo_count + 1;
            that.new_list[key].vo[index].voters = that.new_list[key].vo[index].voters + 1;
          } else {
            uni.showModal({
              title: '提示',
              content: res.data.msg,
              showCancel: false
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    agree(rows) {
      var ids = [];
      for (var i = 0; i < rows.length; i++) {
        var signAgainReq = new Object();
        signAgainReq.pv_id = rows[i];
        ids.push(signAgainReq);
      }
      return JSON.stringify(ids);
    },
    //点击选项
    dian_option(item) {
      var index = item.currentTarget.dataset.index;
      this.dian_index = index;
      var key = item.currentTarget.dataset.key;
      var info = this.new_list[key];
      if (info.is_vo_check > 0) {
        return;
      }
      var vo = this.new_list[key].vo;
      var vo_id = item.currentTarget.dataset.id;
      var vo_id_list = this.new_list[key].vo_id;
      //console.log(index);
      //console.log(info.vo_id[index]);
      if (info['study_type'] == 4) {
        //单选
        if (vo_id == info['vo_id'][0]) {
          var click = 'new_list[' + key + '].vo_id';
          this.new_list[key].vo_id = [];
          return;
        }
        var click = 'new_list[' + key + '].vo_id';
        this.new_list[key].vo_id = [];
        var click = 'new_list[' + key + '].vo_id';
        this.new_list[key].vo_id = [vo_id];
      } else {
        var vo_id_index = vo_id_list.indexOf(vo_id);
        if (vo_id_list.indexOf(vo_id) != -1) {
          var click = 'new_list[' + key + '].vo_id';
          var vo_id_list = info.vo_id;
          vo_id_list.splice(vo_id_index, 1);
          this.new_list[key].vo_id = vo_id_list;
          return;
        } else {
          var click = 'new_list[' + key + '].vo_id';
          var vo_id_list = info.vo_id;
          vo_id_list.push(vo_id);
          this.new_list[key].vo_id = vo_id_list;
        }
      }
    },
    swiperChange(e) {
      this.swiperCurrent = e.detail.current;
    },
    /**
     * 图片手动滑动时，获取当前的轮播id
     */
    imageChange(e) {
      const that = this;
      that.swiperCurrent = e.currentTarget.id;
    },
    cardSwiper(e) {
      this.cardCur = e.detail.current;
    },
    /**
     * 首页跳转链接
     */
    home_url(dd) {
      console.log(dd);
      var copyright = getApp().globalData.store.getState().copyright;
      var that = this;
      var key = dd.currentTarget.dataset.k; //跳转类型
      var uid = dd.currentTarget.dataset.user_id;
      if (key == 1) {
        //头像跳转
        if (uid == 0) {
          uni.showToast({
            title: '身份已隐藏',
            icon: 'none',
            duration: 2000
          });
          return;
        }
        uni.navigateTo({
          url: '/yl_welore/pages/packageB/my_home/index?id=' + uid
        });
        return;
      }
      if (key == 2) {
        //圈子跳转
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/circle_info/index?id=' + dd.currentTarget.dataset.id
        });
        return;
      }
      var e = app.globalData.getCache("userinfo");
      var checkLogin = app.globalData.checkPhoneLogin(0);
      if (checkLogin == 1) {
        this.check_user_login = true;
        return;
      }
      if (checkLogin == 2) {
        this.check_phone_show = true;
        return;
      }
      // if (e.tourist == 0 && copyright.warrant_arbor == 1 && (e.user_phone == null || e.user_phone == '')) {
      //   this.check_phone_show = true;
      //   return;
      // }
      if (key == 3) {
        //内容跳转
        var douyin = app.globalData.__PlugUnitScreen('5fb4baf1f25fe251685b526dc8c30b8f');
        var info = this.new_list[dd.currentTarget.dataset.index];
        if (dd.currentTarget.dataset.type == 2 && info.is_buy == 0 && e.user_phone && douyin) {
          uni.navigateTo({
            url: '/yl_welore/pages/packageF/full_video/index?id=' + dd.currentTarget.dataset.id
          });
          return;
        }
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/article/index?id=' + dd.currentTarget.dataset.id + '&type=' + dd.currentTarget.dataset.type
        });
        return;
      }
    },
    onMyEvent(e, d) {
      this.check_user_login = false;
    },
    showModal(e) {
      this.modalName = e.currentTarget.dataset.target;
    },
    /**
     * 获取评论详情
     */
    get_jubao_text(e) {
      this.home_pl_text = e.detail.value;
    },
    /**
     * 打开首页评论窗口
     */
    home_pl(d) {
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var warrant_arbor = getApp().globalData.store.getState().copyright['warrant_arbor'];
      if (e.tourist == 1 && warrant_arbor == 1) {
        this.check_phone_show = true;
        return;
      }
      uni.navigateTo({
        url: '/yl_welore/pages/packageA/article/index?id=' + d.currentTarget.dataset.id + '&type=' + d.currentTarget.dataset.type
      });
    },
    /**
     * 提交首页评论
     */
    do_user_pl() {
      if (this.home_pl_text == '') {
        uni.showModal({
          title: '提示',
          content: '内容不能为空！',
          showCancel: false
        });
        return;
      }
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      params.text = this.home_pl_text;
      params.id = this.home_pl_id;
      params.reply_type = 0;
      var b = app.globalData.api_root + 'User/add_paper_reply_new';
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == "success") {
            uni.showModal({
              title: '提示',
              content: res.data.msg,
              showCancel: false
            });
            var subscribe = app.globalData.getCache("subscribe");
            if (subscribe && subscribe['YL0004'] && subscribe['YL0006'] && subscribe['YL0007']) {
              app.globalData.authorization(subscribe['YL0004'], subscribe['YL0006'], subscribe['YL0007'], res => { });
            }
            that.hideModal();
          } else {
            uni.showModal({
              title: '提示',
              content: res.data.msg,
              showCancel: false,
              success(r) {
                if (r.confirm) {
                  if (res.data.msg == '请绑定手机号！') {
                    that.home_pl_check = false;
                    that.check_phone_show = true;
                  }
                } else if (r.cancel) {
                  console.log('用户点击取消');
                }
              }
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    /**
     * 关闭
     */
    off_index() {
      this.indexImage_c = false;
      var timestamp = new Date();
      app.globalData.setCache('time', timestamp.getDate());
    },
    top_imageLoad(e) {
      var imgwidth = e.detail.width,
        imgheight = e.detail.height,
        //宽高比  
        ratio = imgwidth / imgheight;
      //计算的高度值  
      var viewHeight = 750 / ratio;
      var imgheight = viewHeight;
      var imgheights = this.imgheights;
      //把每一张图片的对应的高度记录到数组里  
      imgheights[e.target.dataset.id] = imgheight;
      this.imgheights = imgheights;
    },
    bindchange_top(e) {
      this.top_index = e.detail.current;
    },
    imageLoad(e) {
      var $width = e.detail.width,
        $height = e.detail.height,
        ratio = $width / $height;
      console.log(ratio);
      if (ratio > 1) {
        var viewWidth = 345,
          viewHeight = 345 / ratio;
      } else {
        var viewWidth = 150,
          viewHeight = 150 / ratio;
      }
      var image = this.images;
      //将图片的datadata-index作为image对象的key,然后存储图片的宽高值
      image[e.target.dataset.index] = {
        width: viewWidth,
        height: viewHeight > 300 ? 300 : viewHeight
      };
      console.log(viewHeight);
      this.images = image;
    },
    /**
     * 点击话题
     */
    gambit_list(d) {
      // var e = app.globalData.getCache("userinfo");
      // var warrant_arbor = getApp().globalData.store.getState().copyright['warrant_arbor'];
      // if (e.tourist == 1 && warrant_arbor == 1) {
      //   this.check_phone_show = true;
      //   return;
      // }
      var id = d.currentTarget.dataset.id;
      uni.navigateTo({
        url: '/yl_welore/pages/gambit/index?id=' + id
      });
    },
    /**
     * 筛选操作
     */
    handleClickItem1(detail) {
      // wx.pageScrollTo({
      //   selector:"#top_order",
      //   offsetTop:-100
      // })
      console.log('handleClickItem1', detail);
      var index = detail.detail.index;
      var select = this.actions[index];
      console.log(select);
      this.order_time = select['type'];
      this.index_page = 1;
      this.new_list = [];
      this.get_index_list();
    },
    hideModal() {
      this.home_pl_check = false;
    },
    preventTouchMove() { },
    /**
     * 流量主
     */
    get_ad() {
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      var b = app.globalData.api_root + 'User/get_ad';
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == "success") {
            app.globalData.store.setState({
              ad: res.data.data
            });
            that.ad_info = res.data.data.info;
            that.sw_info = res.data.data.info_sw;
            that.info_top = res.data.data.info_top;
            that.info_home = res.data.data.info_home_new;
            var myDate = new Date();
            var old_time = app.globalData.getCache('time');
            console.log(res.data.data.info_top);
            if (res.data.data.info_top && old_time != myDate.getDate()) {
              that.indexImage_c = true;
            } else {
              console.log(2);
              that.indexImage_c = false;
            }
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    /**
     * 动画
     */
    rotate3d(key) {
      var that = this;
      var list = this.new_list;
      // 创建一个动画实例
      var animation_zan = uni.createAnimation({
        // 动画持续时间
        duration: 300,
        // 定义动画效果，当前是匀速
        timingFunction: 'ease'
      });
      // 将该变量赋值给当前动画
      that.animation_zan = animation_zan;
      // 使用缩放动画替代旋转，避免图标反转
      animation_zan.scale(1.2).step();
      list[key]['animationData_zan'] = animation_zan.export();
      that.new_list = list;
      setTimeout(() => {
        var list_g = that.new_list;
        animation_zan.scale(1).step();
        list_g[key]['animationData_zan'] = animation_zan.export();
        that.new_list = list_g;
      }, 150);
    },
    handleChange(detail) {
      var key = detail.currentTarget.dataset.key;
      console.log(key);
      this.leftList = [];
      this.rightList = [];
      this.new_list4 = [];
      this.new_list = [];
      this.index_page = 1;
      this.index_my_page = 1;
      this.index_tj_page = 1;
      if (key == 'tab2') {
        this.get_my_index_list();
      }
      if (key == 'tab1') {
        this.get_index_list_one();
      }
      if (key == 'tab3') {
        this.get_tj_list();
      }
      if (key == 'tab4') {
        this.get_video_list();
      }
      this.current = key;
      this.di_msg = false;
    },
    // get_user_info_async() {
    //   console.log(2);
    //   this.get_user_info();
    // },
    get_index_list_one_async() {
      console.log(3);
      this.get_index_list_one();
    },
    get_ad_async() {
      console.log(4);
      this.get_ad();
    },
    doIt() {
      app.globalData.getLogin(
        // 成功回调 returnA 
        (userInfo) => {
          console.log(' 登录成功:', userInfo);
          this.authority();
          console.log(1);
          this.get_index_list_one_async();
          this.get_ad_async();
          this.get_my_q();
          this.get_top();
          this.get_diy();
        },
        // 失败回调 returnB 
        (err) => {
          console.error(' 登录失败:', err);
        }
      );
    },

    async isLeft() {
      const {
        new_list4,
        leftList,
        rightList
      } = this;

      // 计算当前总数，用于交替分配的索引
      const currentTotal = leftList.length + rightList.length;

      for (let i = 0; i < new_list4.length; i++) {
        const item = new_list4[i];
        const globalIndex = currentTotal + i; // 全局索引

        // 为每个项目添加全局索引
        const itemWithIndex = { ...item, globalIndex: globalIndex };

        // 使用交替分配策略，确保左右平衡
        if (globalIndex % 2 === 0) {
          leftList.push(itemWithIndex);
        } else {
          rightList.push(itemWithIndex);
        }

        // 尝试获取高度，但不依赖它
        await this.getBoxHeight(leftList, rightList);
      }
    },
    /**
     * 更新 leftList 和 rightList 中对应项目的属性
     */
    updateListsItem(globalIndex, property, value) {
      // 在 leftList 中查找并更新
      const leftItem = this.leftList.find(item => item.globalIndex === globalIndex);
      if (leftItem) {
        this.$set(leftItem, property, value);
      }

      // 在 rightList 中查找并更新
      const rightItem = this.rightList.find(item => item.globalIndex === globalIndex);
      if (rightItem) {
        this.$set(rightItem, property, value);
      }
    },
    getBoxHeight(leftList, rightList) {
      //获取左右两边高度
      return new Promise((resolve, reject) => {
        const indexComponent = this.$refs.indexComp;
        if (!indexComponent) {
          resolve();
          return;
        }

        // 先更新数据
        this.leftList = leftList;
        this.rightList = rightList;

        // 等待DOM更新后再查询
        this.$nextTick(() => {
          const query = uni.createSelectorQuery().in(indexComponent);
          query.select('#left').boundingClientRect();
          query.select('#right').boundingClientRect();
          query.exec(res => {
            if (res && res[0] && res[1]) {
              leftHeight = res[0].height; //获取左边列表的高度
              rightHeight = res[1].height; //获取右边列表的高度
            } else {
              leftHeight = 0;
              rightHeight = 0;
            }
            resolve();
          });
        });
      });
    },
    this_url(d) {
      var id = d.currentTarget.dataset.id;
      uni.navigateTo({
        url: '/yl_welore/pages/packageA/circle_info/index?id=' + id
      });
    },
    /**
     * 圈子下一页
     */
    nex_my_qq() {
      this.my_qq_page = this.my_qq_page + 1;
      this.get_my_q();
    },
    get_all_qq() {
      uni.navigateTo({
        url: '/yl_welore/pages/square/index'
      });
    },
    get_top() {
      return new Promise((resolve, reject) => {
        var e = app.globalData.getCache("userinfo");
        var params = new Object();
        params.token = e.token;
        params.openid = e.openid;
        var b = app.globalData.api_root + 'Scanning/paper_heat_config';
        http.POST(b, {
          params: params,
          success: (res) => {
            console.log(res);
            resolve(res);
            this.put_top_list = res.data.list;
            this.put_top_info = res.data.info;
          },
          fail: () => {
            uni.showModal({
              title: '提示',
              content: '网络繁忙，请稍候重试！',
              showCancel: false,
              success: (res) => { }
            });
          }
        });
      });
    },
    get_my_q() {
      return new Promise((resolve, reject) => {
        var e = app.globalData.getCache("userinfo");
        var params = new Object();
        params.token = e.token;
        params.openid = e.openid;
        params.uid = e.uid;
        params.get_id = -1;
        params.page = this.my_qq_page;
        var b = app.globalData.api_root + 'User/get_right_needle';
        http.POST(b, {
          params: params,
          success: (res) => {
            console.log(res);
            resolve(res);
            if (res.data.status == "success") {
              if (res.data.info.length == 0) {
                this.not_jia = true;
              }
              this.my_qq_list.push(...res.data.info);
            } else {
              uni.showToast({
                title: res.data.msg,
                icon: 'none',
                duration: 2000
              });
            }
          },
          fail: () => {
            uni.showModal({
              title: '提示',
              content: '网络繁忙，请稍候重试！',
              showCancel: false,
              success: (res) => { }
            });
          }
        });
      });
    },

    /**
     * 信息站点
     */
    authority() {
      var b = app.globalData.api_root + 'User/get_authority';
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          this.copyright = res.data;
          app.globalData.store.setState({
            copyright: res.data
          });
          this.getHomeFj();
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },

    get_diy() {
      var b = app.globalData.api_root + 'User/get_diy';
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      // params.uid = e.uid;
      params.token = e.token;
      params.openid = e.openid;
      http.POST(b, {
        params: params,
        success: res => {
          console.log(app.globalData);
          app.globalData.store.setState({
            diy: res.data
          });
          var n = parseInt(+new Date() / 1000) + 129600;
          uni.setStorageSync("is_diy", res.data);
          app.globalData.setCache('over_time', n);
          app.globalData.editTabbar();
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    /**
     * 点赞
     */
    add_zan(data) {
      if (this.scene == '1154') {
        return;
      }
      var e = app.globalData.getCache("userinfo");
      var checkLogin = app.globalData.checkPhoneLogin(0);
      if (checkLogin == 1) {
        this.check_user_login = true;
        return;
      }
      if (checkLogin == 2) {
        this.check_phone_show = true;
        return;
      }
      var id = data.currentTarget.dataset.id;
      var key = data.currentTarget.dataset.key;
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.id = id;
      params.uid = e.uid;
      params.applaud_type = 0;
      params.zan_type = this.new_list[key]['is_info_zan'] == true ? 1 : 0;
      var list = this.new_list;
      uni.vibrateShort();

      if (list[key]['is_info_zan'] == false) {
        // 先计算新的值
        const newZanStatus = true;
        const newZanCount = list[key]['info_zan_count_this'] + 1;

        // 同时更新所有数据源
        this.$set(this.new_list[key], 'is_info_zan', newZanStatus);
        this.$set(this.new_list[key], 'info_zan_count_this', newZanCount);
        this.updateListsItem(key, 'is_info_zan', newZanStatus);
        this.updateListsItem(key, 'info_zan_count_this', newZanCount);
      } else {
        // 先计算新的值
        const newZanStatus = false;
        const newZanCount = list[key]['info_zan_count_this'] - 1 < 0 ? 0 : list[key]['info_zan_count_this'] - 1;

        // 同时更新所有数据源
        this.$set(this.new_list[key], 'is_info_zan', newZanStatus);
        this.$set(this.new_list[key], 'info_zan_count_this', newZanCount);
        this.updateListsItem(key, 'is_info_zan', newZanStatus);
        this.updateListsItem(key, 'info_zan_count_this', newZanCount);
      }
      this.rotate3d(key);
      var b = app.globalData.api_root + 'User/add_user_zan';
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == "success") {
            //wx.vibrateShort();
            // list[key]['is_info_zan'] = res.data.info_zan;
            //this.rotate3d(key);
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    /**
     * 首页推荐列表
     */
    get_tj_list() {
      var b = app.globalData.api_root + 'User/get_index_tj_list';
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      params.index_tj_page = this.index_tj_page;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == 'success') {
            this.new_list.push(...res.data.info);
            this.new_list4 = res.data.info;
            if (res.data.info.length == 0) {
              this.di_msg = true;
            }
            this.$nextTick(() => {
              this.isLeft();
            });
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    /**
     * 首页数据
     */
    get_index_list_one() {
      var b = app.globalData.api_root + 'User/get_index_list';
      var e = app.globalData.getCache("userinfo");
      //console.log(e);
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      params.order_time = this.order_time;
      params.index_page = 1;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == 'success') {
            if (res.data.info.length <= 3) {
              this.di_msg = true;
            }
            this.new_list4 = res.data.info;
            this.new_list = res.data.info;
            this.home_list = res.data.home_list;
            this.version = res.data.version;
            this.$nextTick(() => {
              this.isLeft();
            });
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    /**
     * 首页关注数据（page）
     */
    get_my_index_list() {
      var b = app.globalData.api_root + 'User/get_my_index_list';
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.index_page = this.index_my_page;
      params.uid = e.uid;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == 'success') {
            for (var i = 0; i < res.data.info.length; i++) {
              this.new_list.push(res.data.info[i]);
            }
            this.new_list4 = res.data.info;
            if (res.data.info.length == 0 || this.new_list.length < 3) {
              this.di_msg = true;
            }
            this.$nextTick(() => {
              this.isLeft();
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    /**
     * 首页数据（page）
     */
    get_index_list() {
      var b = app.globalData.api_root + 'User/get_index_list';
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.index_page = this.index_page;
      params.order_time = this.order_time;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == 'success') {
            for (var i = 0; i < res.data.info.length; i++) {
              this.new_list.push(res.data.info[i]);
            }
            this.new_list4 = res.data.info;
            if (res.data.info.length == 0) {
              this.di_msg = true;
            }
            this.$nextTick(() => {
              this.isLeft();
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    /**
     * 打开地图
     */
    get_position(d) {
      var a = Number(d.currentTarget.dataset.latitude);
      var o = Number(d.currentTarget.dataset.longitude);
      var name = d.currentTarget.dataset.pos_name;
      if (a && o) {
        uni.openLocation({
          latitude: a,
          longitude: o,
          name: name
        });
      }
    },


    //播放声音
    play(e) {
      var checkLogin = app.globalData.checkPhoneLogin(0);
      if (checkLogin == 1) {
        this.check_user_login = true;
        return;
      }
      if (checkLogin == 2) {
        this.check_phone_show = true;
        return;
      }
      var index = e.currentTarget.dataset.key;
      var nuw = this.new_list;
      var key = 1;
      var info = this.new_list[index];
      if (info['check_look'] == 0 && info['is_buy'] > 0) {
        innerAudioContext.stop();
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/article/index?id=' + info['id'] + '&type=' + info['study_type']
        });
        return;
      }
      uni.getBackgroundAudioPlayerState({
        success(res) {
          console.log(res);
          const status = res.status;
          key = res.status;
        }
      });
      for (var i = 0; i < nuw.length; i++) {
        nuw[i]['is_voice'] = false;
      }
      this.new_list = nuw;
      console.log('播放');
      innerAudioContext.src = e.currentTarget.dataset.vo;
      innerAudioContext.title = nuw[index]['user_nick_name'] + '上传的音乐';
      innerAudioContext.onTimeUpdate(() => {
        //console.log(innerAudioContext.currentTime)
        var duration = innerAudioContext.duration;
        var offset = innerAudioContext.currentTime;
        var currentTime = parseInt(innerAudioContext.currentTime);
        var min = "0" + parseInt(currentTime / 60);
        var sec = currentTime % 60;
        if (sec < 10) {
          sec = "0" + sec;
        }
        ;
        var starttime = min + ':' + sec; /*  00:00  */

        this.$set(this.new_list[index], 'starttime', starttime)
        this.$set(this.new_list[index], 'offset', offset)

      });
      // innerAudioContext.play();

      this.$set(this.new_list[index], 'is_voice', true);
      this.new_list_index = index;
      //播放结束
      innerAudioContext.onEnded(() => {
        this.$set(this.new_list[index], 'is_voice', false)
        this.starttime = '00:00';
        this.offset = 0;
        console.log("音乐播放结束");
      });
      innerAudioContext.play();
    },
    /**
     * 停止
     */
    stop(e) {
      innerAudioContext.pause();
      console.log('暂停');
      var index = e.currentTarget.dataset.key;
      this.$set(this.new_list[index], 'is_voice', false)
    },
    // 进度条拖拽
    sliderChange(e) {
      var index = e.currentTarget.dataset.key;
      var info = this.new_list[index];
      if (info['check_look'] == 0 && info['is_buy'] == 1) {
        innerAudioContext.stop();
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/article/index?id=' + info['id'] + '&type=' + info['study_type']
        });
        return;
      }
      var offset = parseInt(e.detail.value);
      innerAudioContext.play();
      innerAudioContext.seek(offset);
      this.$set(this.new_list[index], 'is_voice', true)
    },

    /**
     * 是否禁止转发
     */
    check_share() {
      uni.showToast({
        title: '该帖禁止转发',
        icon: 'none',
        duration: 2000
      });
      return;
    },

    /**
     * 用户转发记录
     */
    user_forward(id) {
      var b = app.globalData.api_root + 'Task/user_forwarded';
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.id = id;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    handleDynamicCode(event, type) {
      console.log(event);
      console.log(type);
      if (type === 'check_share') {
        this.check_share();
      } else if (type === 'add_zan') {
        this.add_zan(event);
      }
    }
  },
};
</script>
<style>
@import "../../colorui/animation.css";

.sticky {
  position: fixed !important;
  top: 7%;
  width: 100%;
  margin-top: 30rpx;
}

.botton_text {
  font-weight: 500;
  margin-top: 10rpx;
}

.tab_home {
  height: 25px !important;
  border-radius: 25px;
}

.ui-tab-mark {
  background-color: #ffffff;
  border-radius: 25px !important;
}

.ui-tab-text {
  font-size: 12px;
}

._no_index5 {
  color: #A5A6B0;
}

._this_index5 {
  color: #000000;
}

._this {
  font-weight: 600;
  font-size: 20px;
}

.banner-swiper {
  width: 100%;
  height: 500rpx;
  overflow: hidden;
}

.slide-image {
  width: 96%;
  display: block;
  margin: 0 auto;
  height: 450rpx;
}

.active {
  margin-top: 0rpx;
  height: 500rpx;
}

page {
  background-color: #fff;
}

.pinglunzan {
  margin-top: 20rpx;
}

.demo-row {
  padding: 10px 0px;
}

.yes_pos {
  position: relative;
}

.placeholder {
  padding: 0rpx 5px 5rpx 5rpx;
}

.img-style {
  height: 0rpx;
  width: 0rpx;
  position: absolute;
  right: 50%;
  opacity: 0;
  bottom: 0px;
}

.weui-tabbar_boo {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  position: fixed;
  bottom: 0;
  width: 100%;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
}

.weui-tabbar_boo_no {
  /* display: -webkit-box;
  display: -webkit-flex; */
  position: fixed;
  bottom: 23%;
  right: 0;
  /* width: 100%; */
}

/**
     * 弹窗
     */

.show-btn {
  margin-top: 100rpx;
  color: #2c2;
}

.modal-mask {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  opacity: 0.5;
  overflow: hidden;
  z-index: 888;
  color: #fff;
}

.modal-dialog {
  width: 540rpx;
  position: fixed;
  top: 45%;
  left: 0;
  z-index: 999;
  background: #f9f9f9;
  margin: -180rpx 105rpx;
  border-radius: 10px;
}

.modal-title {
  padding-top: 50rpx;
  font-size: 36rpx;
  color: #030303;
  text-align: center;
}

.modal-content {
  padding: 20rpx 32rpx;
}

.modal-input {
  display: flex;
  background: #fff;
  border: 2rpx solid #ddd;
  border-radius: 4rpx;
  font-size: 28rpx;
}

.input {
  width: 100%;
  height: 82rpx;
  font-size: 28rpx;
  line-height: 28rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  color: #333;
}

input-holder {
  color: #666;
  font-size: 28rpx;
}

.modal-footer {
  display: flex;
  flex-direction: row;
  height: 86rpx;
  border-top: 1px solid #dedede;
  font-size: 34rpx;
  line-height: 86rpx;
}

.btn-cancel {
  width: 50%;
  color: #666;
  text-align: center;
  border-right: 1px solid #dedede;
}

.btn-confirm {
  width: 50%;
  color: #c33;
  text-align: center;
}

.li1 {
  border-radius: 10rpx;
  height: 310rpx;
  transform: scale(0.9);
  width: 100%;
}

.selected {
  transform: scale(0.9);
}

.border_r {
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
}

.falseee {
  padding: 10px 0px;
  overflow: hidden;
  display: flex;
  /*弹性盒子*/
  flex-flow: row wrap;
  /*子元素溢出父容器时换行*/
}

.falseee:after {
  content: "";
}

.col-4 {
  width: calc(100%/4);
  float: left;
  text-align: center;
  margin-bottom: calc(5px*4 /2);
  font-size: 12px;
}

/*按钮大小  */
.audioOpen {
  width: 50rpx;
  height: 50rpx;
  border: 1px solid #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  margin-top: 57rpx;
}

.image2 {
  margin-left: 10%;
}

/*进度条长度  */
.slid {
  flex: 1;
  position: relative;
}

.slid view {
  display: flex;
  justify-content: space-between;
}

.slid view>text:nth-child(1) {
  color: #4c9dee;
  margin-left: 6rpx;
}

.slid view>text:nth-child(2) {
  margin-right: 6rpx;
}

slider {
  width: 520rpx;
  margin: 0;
  margin-left: 35rpx;
}

/*横向布局  */
.times {
  width: 100rpx;
  text-align: center;
  display: inline-block;
  font-size: 24rpx;
  color: #999999;
  margin-top: 5rpx;
}

.title view {
  text-indent: 2em;
}

.bf {
  font-weight: 600;
  background-image: linear-gradient(to top, #d5d4d0 0%, #d5d4d0 1%, #eeeeec 31%, #efeeec 75%, #e9e9e7 100%);
  color: transparent;
  -webkit-background-clip: text;
  animation: ran 20s linear infinite;
}

.flex {
  display: flex;
  align-items: flex-start;
}

#left,
#right {
  width: 48%;
  margin: 0 1%;
}

/* 客服按钮样式 */
.customer-service-container {
  position: fixed;
  right: 20rpx;
  bottom: 20%;
  z-index: 999;
}

.customer-service-btn {
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border-radius: 60rpx;
  border: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  animation: pulse 2s infinite;
}

.customer-service-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border-radius: 60rpx;
  animation: ripple 3s infinite;
  z-index: -1;
}

.customer-service-btn::after {
  border: none;
}

.customer-service-btn-hover {
  transform: scale(0.95);
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.5);
}

.customer-service-icon {
  font-size: 30rpx;
  margin-bottom: 4rpx;
  color: #ffffff;
  animation: bounce 1.5s infinite;
}

.customer-service-text {
  font-size: 20rpx;
  color: #ffffff;
  font-weight: 500;
}

/* 动画效果 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }

  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-6rpx);
  }

  60% {
    transform: translateY(-3rpx);
  }
}
</style>